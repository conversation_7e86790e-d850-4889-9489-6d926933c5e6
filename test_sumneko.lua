-- Sumneko Lua Language Server 测试文件
print("=== Sumneko Lua 测试 ===")

-- 测试变量和类型推断
local name = "Sumneko Lua"
local version = 5.1
local isWorking = true

print("扩展名称: " .. name)
print("Lua版本: " .. version)
print("工作状态: " .. tostring(isWorking))

-- 测试函数定义和调用
local function greet(userName)
    return "Hello, " .. userName .. "!"
end

-- 测试表操作
local coordinates = {
    x = 10,
    y = 20,
    z = 30
}

-- 测试循环
print("\n坐标信息:")
for key, value in pairs(coordinates) do
    print(key .. " = " .. value)
end

-- 测试数组
local numbers = {1, 2, 3, 4, 5}
print("\n数字数组:")
for i, num in ipairs(numbers) do
    print("numbers[" .. i .. "] = " .. num)
end

-- 测试字符串操作
local message = greet("Lua开发者")
print("\n" .. message)

-- 测试数学运算
local result = coordinates.x + coordinates.y + coordinates.z
print("坐标总和: " .. result)

print("\n=== 测试完成 ===")
print("如果您看到这条消息并且没有语法错误提示，")
print("说明Sumneko Lua Language Server配置成功！")
