-- Tencent Lua 插件测试文件
print("=== Tencent Lua 插件测试 ===")
print("Lua版本测试:")
print(_VERSION)

-- 测试基本功能
local function test_basic()
    print("\n基本功能测试:")
    local coords = {1, 2, 3}
    for i, v in ipairs(coords) do
        print("坐标[" .. i .. "] = " .. v)
    end
end

-- 测试字符串操作
local function test_string()
    print("\n字符串测试:")
    local str = "Hello, Tencent Lua!"
    print("原字符串: " .. str)
    print("长度: " .. string.len(str))
    print("大写: " .. string.upper(str))
end

-- 执行测试
test_basic()
test_string()

print("\n=== 测试完成 ===")
print("如果您看到这条消息，说明Tencent Lua插件配置成功！")
