# 在线Lua运行环境

如果您不想在本地安装Lua，可以使用以下在线环境：

## 推荐的在线Lua环境：

1. **Replit** - https://replit.com/languages/lua
   - 功能最全面
   - 支持多文件项目
   - 可以保存和分享代码

2. **OneCompiler** - https://onecompiler.com/lua
   - 界面简洁
   - 快速运行
   - 支持输入输出

3. **Programiz** - https://www.programiz.com/lua/online-compiler/
   - 适合学习
   - 有教程和示例

## 使用方法：
1. 复制您的Lua代码
2. 粘贴到在线编辑器
3. 点击运行按钮

## 您的代码示例：
```lua
local coordinates = {1, 2, 3}
print("坐标[1] = " .. coordinates[1])
print("坐标[2] = " .. coordinates[2]) 
print("坐标[3] = " .. coordinates[3])
print("hello world")
```
