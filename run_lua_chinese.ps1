# PowerShell脚本支持中文Lua运行
param(
    [Parameter(Mandatory=$true)]
    [string]$LuaFile
)

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

Write-Host "正在运行Lua文件: $LuaFile" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Yellow

try {
    & "C:\Program Files (x86)\Lua\5.1\lua.exe" $LuaFile
    Write-Host "================================" -ForegroundColor Yellow
    Write-Host "运行完成！" -ForegroundColor Green
} catch {
    Write-Host "运行出错: $_" -ForegroundColor Red
}

Read-Host "按回车键继续"
