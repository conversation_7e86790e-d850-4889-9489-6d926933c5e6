{"$schema": "https://raw.githubusercontent.com/sumneko/vscode-lua/master/setting/schema.json", "Lua.runtime.version": "Lua 5.1", "Lua.runtime.path": ["?.lua", "?/init.lua", "?/?.lua"], "Lua.workspace.library": ["C:/Program Files (x86)/Lua/5.1"], "Lua.diagnostics.globals": ["vim", "love", "awesome"], "Lua.diagnostics.disable": ["trailing-space", "undefined-global", "lowercase-global", "unused-local", "unused-vararg", "unused-function", "redefined-local", "close-non-object", "duplicate-index", "empty-block"], "Lua.diagnostics.severity": {"undefined-global": "Hint", "lowercase-global": "Hint"}, "Lua.runtime.unicodeName": true, "Lua.runtime.nonstandardSymbol": ["//", "/**/", "`", "+=", "-=", "*=", "/=", "%=", "^=", "//=", "|=", "&=", "<<=", ">>=", "||", "&&", "!", "!=", "!=="], "Lua.completion.enable": true, "Lua.completion.callSnippet": "Both", "Lua.completion.keywordSnippet": "Both", "Lua.hover.enable": true, "Lua.signatureHelp.enable": true, "Lua.format.enable": true, "Lua.telemetry.enable": false}