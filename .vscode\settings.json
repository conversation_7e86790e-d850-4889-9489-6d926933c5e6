{"Lua.runtime.version": "Lua 5.1", "Lua.runtime.path": ["?.lua", "?/init.lua", "?/?.lua"], "Lua.workspace.library": ["C:\\Program Files (x86)\\Lua\\5.1\\lua"], "Lua.workspace.maxPreload": 5000, "Lua.workspace.preloadFileSize": 50, "Lua.diagnostics.globals": ["vim"], "Lua.completion.enable": true, "Lua.completion.callSnippet": "Both", "Lua.completion.keywordSnippet": "Both", "Lua.hover.enable": true, "Lua.signatureHelp.enable": true, "files.encoding": "utf8", "files.associations": {"*.lua": "lua"}, "terminal.integrated.defaultProfile.windows": "PowerShell", "code-runner.executorMap": {"lua": "\"C:\\Program Files (x86)\\Lua\\5.1\\lua.exe\""}}