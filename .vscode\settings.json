{"luahelper.luapath": "C:\\Program Files (x86)\\Lua\\5.1\\lua.exe", "luahelper.scriptRoots": ["${workspaceFolder}"], "luahelper.enableLuacheck": true, "files.encoding": "utf8", "files.associations": {"*.lua": "lua"}, "terminal.integrated.defaultProfile.windows": "PowerShell", "security.workspace.trust.untrustedFiles": "open", "security.workspace.trust.enabled": false, "debug.allowBreakpointsEverywhere": true}